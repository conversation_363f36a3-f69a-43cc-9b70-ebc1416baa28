#!/usr/bin/env python3
"""
Test script for UpdateAgentCombined knowledge functionality.

This test verifies that the UpdateAgentCombined method can properly handle
files and URLs updates in the knowledge base.
"""

import os
import sys
from datetime import datetime, timezone
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

from google.protobuf.field_mask_pb2 import FieldMask

from app.db.session import SessionLocal
from app.grpc import agent_pb2
from app.models.agent import AgentConfig, AgentConfigVersion, AgentKnowledgeBase
from app.services.agent_functions import AgentFunctionsService
from app.utils.constants.constants import (
    AgentCategoryEnum,
    AgentOwnerTypeEnum,
    AgentStatusEnum,
    AgentVisibilityEnum,
)


def test_update_agent_combined_knowledge():
    """Test updating knowledge (files and URLs) through UpdateAgentCombined"""

    print("=== Test: UpdateAgentCombined - Knowledge Updates ===")

    agent_functions = AgentFunctionsService()
    db = SessionLocal()

    try:
        # Create a test agent with knowledge base
        test_agent = AgentConfig(
            id="test-knowledge-agent-123",
            name="Knowledge Test Agent",
            description="Test description",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
        )

        # Create knowledge base
        knowledge_base = AgentKnowledgeBase(
            files=[
                {
                    "file": "gs://bucket/old-file1.pdf",
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "size": 1024,
                }
            ],
            urls=[
                {
                    "url": "https://example.com/old-url",
                    "created_at": datetime.now(timezone.utc).isoformat(),
                }
            ],
        )

        db.add(knowledge_base)
        db.add(test_agent)  # Add agent first
        db.flush()

        # Create agent version
        agent_version = AgentConfigVersion(
            agent_config_id=test_agent.id,
            knowledge_base_id=knowledge_base.id,
            name="Test Version",
            description="Test version description",
            avatar="https://example.com/avatar.png",
            system_message="Test system message",
        )

        db.add(agent_version)
        db.flush()

        test_agent.current_version_id = agent_version.id
        db.add(test_agent)
        db.commit()

        print(f"✅ Created test agent with knowledge base")

        # Create update request with new files and URLs
        update_request = agent_pb2.UpdateAgentCombinedRequest()
        update_request.agent_id = test_agent.id
        update_request.owner.id = "test-user-123"

        # Set new files and URLs
        update_request.files.extend(["gs://bucket/new-file1.pdf", "gs://bucket/new-file2.docx"])
        update_request.urls.extend(["https://example.com/new-url1", "https://example.com/new-url2"])

        # Set field mask
        field_mask = FieldMask()
        field_mask.paths.extend(["files", "urls"])
        update_request.update_mask.CopyFrom(field_mask)

        context = Mock()

        # Mock the file size method to avoid actual GCS calls
        with patch.object(agent_functions, "_get_gcs_file_size", return_value=2048):
            # Execute update
            response = agent_functions.UpdateAgentCombined(update_request, context)

        print(f"Update response: {response.success} - {response.message}")

        assert response.success, f"Update failed: {response.message}"

        # Verify changes were applied to knowledge base
        updated_knowledge_base = (
            db.query(AgentKnowledgeBase).filter(AgentKnowledgeBase.id == knowledge_base.id).first()
        )

        # Check files were updated
        assert len(updated_knowledge_base.files) == 2
        file_urls = [f["file"] for f in updated_knowledge_base.files]
        assert "gs://bucket/new-file1.pdf" in file_urls
        assert "gs://bucket/new-file2.docx" in file_urls
        assert "gs://bucket/old-file1.pdf" not in file_urls

        # Check URLs were updated
        assert len(updated_knowledge_base.urls) == 2
        url_strings = [u["url"] for u in updated_knowledge_base.urls]
        assert "https://example.com/new-url1" in url_strings
        assert "https://example.com/new-url2" in url_strings
        assert "https://example.com/old-url" not in url_strings

        # Check that agent is marked as updated
        updated_agent = db.query(AgentConfig).filter(AgentConfig.id == test_agent.id).first()
        assert updated_agent.is_updated == True

        print("✅ Knowledge base files and URLs updated successfully")

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback

        traceback.print_exc()
        raise
    finally:
        # Cleanup
        db.query(AgentConfigVersion).filter(
            AgentConfigVersion.agent_config_id == "test-knowledge-agent-123"
        ).delete()
        db.query(AgentKnowledgeBase).filter(AgentKnowledgeBase.id == knowledge_base.id).delete()
        db.query(AgentConfig).filter(AgentConfig.id == "test-knowledge-agent-123").delete()
        db.commit()
        db.close()


def test_update_agent_combined_knowledge_no_existing_kb():
    """Test updating knowledge when no knowledge base exists"""

    print("=== Test: UpdateAgentCombined - Knowledge Updates (No Existing KB) ===")

    agent_functions = AgentFunctionsService()
    db = SessionLocal()

    try:
        # Create a test agent without knowledge base
        test_agent = AgentConfig(
            id="test-no-kb-agent-123",
            name="No KB Test Agent",
            description="Test description",
            avatar="https://example.com/avatar.png",
            owner_id="test-user-123",
            owner_type=AgentOwnerTypeEnum.USER,
            is_updated=False,
            visibility=AgentVisibilityEnum.PRIVATE,
            status=AgentStatusEnum.ACTIVE,
            agent_category=AgentCategoryEnum.AI_AGENT,
            system_message="Test system message",
        )

        # Create agent version without knowledge base
        agent_version = AgentConfigVersion(
            agent_config_id=test_agent.id,
            knowledge_base_id=None,  # No knowledge base initially
            name="Test Version",
            description="Test version description",
            avatar="https://example.com/avatar.png",
            system_message="Test system message",
        )

        db.add(test_agent)  # Add agent first
        db.add(agent_version)
        db.flush()

        test_agent.current_version_id = agent_version.id
        db.add(test_agent)
        db.commit()

        print(f"✅ Created test agent without knowledge base")

        # Create update request with files
        update_request = agent_pb2.UpdateAgentCombinedRequest()
        update_request.agent_id = test_agent.id
        update_request.owner.id = "test-user-123"

        # Set new files
        update_request.files.extend(["gs://bucket/first-file.pdf"])

        # Set field mask
        field_mask = FieldMask()
        field_mask.paths.extend(["files"])
        update_request.update_mask.CopyFrom(field_mask)

        context = Mock()

        # Mock the file size method to avoid actual GCS calls
        with patch.object(agent_functions, "_get_gcs_file_size", return_value=1024):
            # Execute update
            response = agent_functions.UpdateAgentCombined(update_request, context)

        print(f"Update response: {response.success} - {response.message}")

        assert response.success, f"Update failed: {response.message}"

        # Verify knowledge base was created
        updated_version = (
            db.query(AgentConfigVersion).filter(AgentConfigVersion.id == agent_version.id).first()
        )

        assert updated_version.knowledge_base_id is not None

        # Check the created knowledge base
        created_kb = (
            db.query(AgentKnowledgeBase)
            .filter(AgentKnowledgeBase.id == updated_version.knowledge_base_id)
            .first()
        )

        assert created_kb is not None
        assert len(created_kb.files) == 1
        assert created_kb.files[0] == "gs://bucket/first-file.pdf"

        print("✅ Knowledge base created successfully when none existed")

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback

        traceback.print_exc()
        raise
    finally:
        # Cleanup
        db.query(AgentConfigVersion).filter(
            AgentConfigVersion.agent_config_id == "test-no-kb-agent-123"
        ).delete()
        if "created_kb" in locals() and created_kb:
            db.query(AgentKnowledgeBase).filter(AgentKnowledgeBase.id == created_kb.id).delete()
        db.query(AgentConfig).filter(AgentConfig.id == "test-no-kb-agent-123").delete()
        db.commit()
        db.close()


def run_all_tests():
    """Run all knowledge update tests"""

    print("🧪 Running UpdateAgentCombined knowledge functionality tests...")

    tests = [
        test_update_agent_combined_knowledge,
        test_update_agent_combined_knowledge_no_existing_kb,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            test()
            passed += 1
            print(f"✅ {test.__name__} PASSED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED with exception: {str(e)}\n")

    print(f"🏁 Test Results: {passed} passed, {failed} failed")

    if failed > 0:
        print("❌ Some tests failed!")
        return False
    else:
        print("✅ All tests passed!")
        return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
