import json
from typing import Any, Dict, List, Optional, Union

import structlog
from sqlalchemy import and_, or_
from sqlalchemy.orm import Query, Session, joinedload, selectinload

from app.grpc import agent_pb2
from app.models.agent import (
    AgentAvatar,
    AgentCapabilities,
    AgentConfig,
    AgentConfigVersion,
    AgentMarketplaceListing,
)
from app.models.provider import Model, Provider  # Import Model and Provider

logger = structlog.get_logger()


def _agent_avatar_to_protobuf(avatar: AgentAvatar) -> agent_pb2.AgentAvatar:
    """Convert AgentAvatar model to protobuf AgentAvatar message"""
    # Handle both string and datetime objects for created_at and updated_at
    created_at = avatar.created_at
    if hasattr(created_at, "isoformat"):
        created_at = created_at.isoformat()

    updated_at = avatar.updated_at
    if hasattr(updated_at, "isoformat"):
        updated_at = updated_at.isoformat()

    return agent_pb2.AgentAvatar(
        id=str(avatar.id),
        url=avatar.url,
        created_at=created_at,
        updated_at=updated_at,
    )


def _agents_to_protobuf(
    db: Session,
    agent_ids: Union[str, List[str], None] = None,
    filters: Optional[Dict[str, Any]] = None,
    pagination: Optional[Dict[str, int]] = None,
) -> Union[agent_pb2.Agent, List[agent_pb2.Agent], Dict[str, Any]]:
    """
    Convert AgentConfig model(s) to protobuf Agent message(s) with optimized queries.

    Args:
        db: Database session
        agent_ids: Single agent ID, list of agent IDs, or None for filtered queries
        filters: Dictionary of filters to apply (optional)
        pagination: Dictionary with 'page', 'page_size' for pagination (optional)

    Returns:
        - Single Agent protobuf (if single agent_id provided)
        - List of Agent protobufs (if multiple agent_ids or filters provided)
        - Dict with agents, total, page info (if pagination provided)
    """
    try:
        # Determine return type
        return_single = isinstance(agent_ids, str)
        return_paginated = pagination is not None

        # Normalize agent_ids to list if provided
        if isinstance(agent_ids, str):
            agent_ids = [agent_ids]

        # Build base query with optimized joins
        agents_query = db.query(AgentConfig).options(
            # Eager load current version with all its relationships
            joinedload(AgentConfig.current_version).options(
                joinedload(AgentConfigVersion.model_config),
                joinedload(AgentConfigVersion.knowledge_base),
            ),
            # Eager load variables
            selectinload(AgentConfig.variables),
        )

        # Apply agent ID filter if provided
        if agent_ids:
            agents_query = agents_query.filter(AgentConfig.id.in_(agent_ids))

        # Apply additional filters
        if filters:
            agents_query = _apply_filters(agents_query, filters)

        # Handle pagination
        total = None
        total_pages = None
        page = None

        if return_paginated:
            # Get total count before applying pagination
            total = agents_query.count()
            page = pagination.get("page", 1)
            page_size = pagination.get("page_size", 10)

            # Calculate total pages
            total_pages = (total + page_size - 1) // page_size

            # Apply pagination
            agents_query = agents_query.offset((page - 1) * page_size).limit(page_size)

        # Execute query
        agents = agents_query.all()

        if not agents:
            if return_single:
                return None
            elif return_paginated:
                return {"agents": [], "total": 0, "page": page, "total_pages": 0}
            else:
                return []

        # Get all unique model providers and names for batch lookup
        model_lookups = set()
        capabilities_ids = set()

        for agent in agents:
            model_provider, model_name = _get_model_info(agent)
            if model_provider and model_name:
                model_lookups.add((model_provider, model_name))

            # Collect capabilities IDs for batch lookup
            if agent.capabilities_id:
                capabilities_ids.add(agent.capabilities_id)

        # Batch query for all models
        model_cache = {}
        if model_lookups:
            model_conditions = [
                and_(Provider.provider == provider, Model.model == model_name)
                for provider, model_name in model_lookups
            ]

            models = (
                db.query(Model, Provider.provider)
                .join(Provider)
                .filter(or_(*model_conditions))
                .all()
            )

            for model, provider in models:
                model_cache[(provider, model.model)] = model

        # Batch query for all capabilities
        capabilities_cache = {}
        if capabilities_ids:
            capabilities_list = (
                db.query(AgentCapabilities).filter(AgentCapabilities.id.in_(capabilities_ids)).all()
            )

            for capability in capabilities_list:
                capabilities_cache[capability.id] = capability

        # Convert all agents to protobuf
        result_agents = []
        for agent in agents:
            agent_proto = _convert_single_agent_to_protobuf(agent, model_cache, capabilities_cache)
            result_agents.append(agent_proto)

        # Return appropriate format
        if return_single:
            return result_agents[0]
        elif return_paginated:
            return {
                "agents": result_agents,
                "total": total,
                "page": page,
                "total_pages": total_pages,
            }
        else:
            return result_agents

    except Exception as e:
        logger.error(f"Error converting AgentConfig(s) to protobuf: {e}")
        raise e


def _apply_filters(query: Query, filters: Dict[str, Any]) -> Query:
    """Apply filters to the AgentConfig query."""

    # Owner filter
    if filters.get("owner_id"):
        query = query.filter(AgentConfig.owner_id == filters["owner_id"])

    # Department filter
    if filters.get("department"):
        query = query.filter(AgentConfig.department == filters["department"])

    # Organization filter
    if filters.get("organization_id"):
        query = query.filter(AgentConfig.organization_id == filters["organization_id"])

    # Status filter
    if filters.get("status"):
        query = query.filter(AgentConfig.status == filters["status"])

    # Visibility filter
    if filters.get("visibility"):
        query = query.filter(AgentConfig.visibility == filters["visibility"])

    # Boolean filters
    if filters.get("is_bench_employee") is not None:
        query = query.filter(AgentConfig.is_bench_employee == filters["is_bench_employee"])

    if filters.get("is_customizable") is not None:
        query = query.filter(AgentConfig.is_customizable == filters["is_customizable"])

    if filters.get("is_a2a") is not None:
        query = query.filter(AgentConfig.is_a2a == filters["is_a2a"])

    # Search filter (name or description)
    if filters.get("search"):
        search_value = f"%{filters['search']}%"
        query = query.filter(
            or_(AgentConfig.name.ilike(search_value), AgentConfig.description.ilike(search_value))
        )

    return query


def _get_model_info(agent: AgentConfig) -> tuple[str, str]:
    """Extract model provider and name from agent, prioritizing current version."""
    model_provider = ""
    model_name = ""

    # Try current version first
    if agent.current_version and agent.current_version.model_config:
        model_provider = agent.current_version.model_config.model_provider or ""
        model_name = agent.current_version.model_config.model_name or ""

    # No fallback to legacy fields since they don't exist anymore
    return model_provider, model_name


def _convert_single_agent_to_protobuf(
    agent: AgentConfig,
    model_cache: Dict[tuple, Model],
    capabilities_cache: Dict[str, AgentCapabilities],
) -> agent_pb2.Agent:
    """Convert a single AgentConfig to protobuf with cached model data."""

    # Initialize defaults
    model_provider = ""
    model_name = ""
    temperature = 0.0
    max_tokens = 0
    files = []
    urls = []

    # Get data from current version if available
    if agent.current_version:
        current_version = agent.current_version

        # Model config from current version
        if current_version.model_config:
            model_provider = current_version.model_config.model_provider or ""
            model_name = current_version.model_config.model_name or ""
            temperature = (
                current_version.model_config.temperature
                if current_version.model_config.temperature is not None
                else temperature
            )
            max_tokens = (
                current_version.model_config.max_tokens
                if current_version.model_config.max_tokens is not None
                else max_tokens
            )

        # Knowledge base from current version
        if current_version.knowledge_base:
            files = current_version.knowledge_base.files or []
            urls = current_version.knowledge_base.urls or []

    # No fallback to legacy fields since they don't exist anymore

    # Create main agent protobuf
    agent_proto = agent_pb2.Agent(
        id=str(agent.id),
        name=agent.name or "",
        description=agent.description or "",
        avatar=agent.avatar or "",
        owner_id=agent.owner_id or "",
        user_ids=agent.user_ids if agent.user_ids else [],
        owner_type=agent.owner_type or "",
        is_bench_employee=bool(agent.is_bench_employee),
        is_imported=bool(agent.is_imported),
        agent_category=agent.agent_category or "",
        system_message=agent.system_message or "",
        model_provider=model_provider,
        model_name=model_name,
        temperature=temperature,
        max_tokens=max_tokens,
        workflow_ids=agent.workflow_ids if agent.workflow_ids else [],
        mcp_server_ids=agent.mcp_server_ids if agent.mcp_server_ids else [],
        agent_topic_type=agent.agent_topic_type or "",
        visibility=agent.visibility or "",
        tags=agent.tags if agent.tags else [],
        status=agent.status or "",
        created_at=agent.created_at.isoformat(),
        updated_at=agent.updated_at.isoformat(),
        department=agent.department or "",
        organization_id=agent.organization_id or "",
        tone=agent.tone or "",
        files=[
            agent_pb2.FileEntry(
                file=f.get("file", ""),
                created_at=f.get("created_at", ""),
                size=f.get("size", 0),
            )
            for f in files
        ],
        urls=[
            agent_pb2.UrlEntry(url=u.get("url", ""), created_at=u.get("created_at", ""))
            for u in urls
        ],
        is_changes_marketplace=(
            bool(agent.is_changes_marketplace)
            if agent.is_changes_marketplace is not None
            else False
        ),
        is_a2a=bool(agent.is_a2a) if agent.is_a2a is not None else False,
        is_customizable=(
            bool(agent.is_customizable) if agent.is_customizable is not None else False
        ),
        capabilities_id=str(agent.capabilities_id) if agent.capabilities_id else "",
        example_prompts=agent.example_prompts if agent.example_prompts else [],
        category=agent.category or "",
        is_updated=agent.is_updated if agent.is_updated else False,
    )

    # Populate model_data using cached model info
    model_data_proto = agent_pb2.ModelData()
    model_data_proto.model_provider = model_provider
    model_data_proto.model_name = model_name
    model_data_proto.temperature = temperature
    model_data_proto.max_tokens = max_tokens

    # Use cached model data
    if model_provider and model_name:
        model_entry = model_cache.get((model_provider, model_name))
        if model_entry:
            model_data_proto.model_id = str(model_entry.id)
            model_data_proto.context_window = (
                model_entry.context_window if model_entry.context_window is not None else 0
            )

    agent_proto.model_data.CopyFrom(model_data_proto)

    # Populate capabilities using cached data
    if agent.capabilities_id:
        db_capabilities = capabilities_cache.get(agent.capabilities_id)
        if db_capabilities:
            capabilities_proto = agent_pb2.AgentCapabilities(
                id=str(db_capabilities.id),
                capabilities=(
                    json.dumps(db_capabilities.capabilities) if db_capabilities.capabilities else ""
                ),
                input_modes=db_capabilities.input_modes if db_capabilities.input_modes else [],
                output_modes=db_capabilities.output_modes if db_capabilities.output_modes else [],
                response_model=(
                    db_capabilities.response_model if db_capabilities.response_model else []
                ),
                created_at=(
                    db_capabilities.created_at.isoformat() if db_capabilities.created_at else ""
                ),
                updated_at=(
                    db_capabilities.updated_at.isoformat() if db_capabilities.updated_at else ""
                ),
            )
            agent_proto.agent_capabilities.CopyFrom(capabilities_proto)

    # Populate variables (already eager loaded)
    if agent.variables:
        logger.debug(f"ORM agent has {len(agent.variables)} variables. Converting to proto.")
        for db_var_orm_item in agent.variables:
            var_data_proto_item = agent_pb2.AgentVariableData(
                id=str(db_var_orm_item.id),
                name=db_var_orm_item.name,
                type=db_var_orm_item.type,
                created_at=db_var_orm_item.created_at.isoformat(),
                updated_at=db_var_orm_item.updated_at.isoformat(),
            )
            if db_var_orm_item.description is not None:
                var_data_proto_item.description = db_var_orm_item.description
            if db_var_orm_item.default_value is not None:
                var_data_proto_item.default_value = db_var_orm_item.default_value

            agent_proto.variables.append(var_data_proto_item)

    return agent_proto


def _agent_to_protobuf(db: Session, agent: AgentConfig) -> agent_pb2.Agent:
    """Convert AgentConfig model to protobuf Agent message"""
    try:
        # Get current version to retrieve model config and knowledge base
        current_version = None
        model_provider = ""
        model_name = ""
        files = []
        urls = []
        temperature = 0.0  # Initialize temperature with a default value
        max_tokens = 0  # Initialize max_tokens with a default value

        if agent.current_version_id:
            current_version = (
                db.query(AgentConfigVersion)
                .options(
                    joinedload(AgentConfigVersion.model_config),
                    joinedload(AgentConfigVersion.knowledge_base),
                )
                .filter(AgentConfigVersion.id == agent.current_version_id)
                .first()
            )

            if current_version:
                # Get model config from current version
                if current_version.model_config_id and current_version.model_config:
                    model_provider = current_version.model_config.model_provider or ""
                    model_name = current_version.model_config.model_name or ""
                    # temperature and max_tokens are already initialized, update if found
                    temperature = (
                        current_version.model_config.temperature
                        if current_version.model_config.temperature is not None
                        else temperature
                    )
                    max_tokens = (
                        current_version.model_config.max_tokens
                        if current_version.model_config.max_tokens is not None
                        else max_tokens
                    )

                # Get knowledge base from current version
                if current_version.knowledge_base_id and current_version.knowledge_base:
                    files = current_version.knowledge_base.files or []
                    urls = current_version.knowledge_base.urls or []

        # No fallback to legacy fields since they don't exist anymore

        agent_proto = agent_pb2.Agent(
            id=str(agent.id),
            name=agent.name or "",
            description=agent.description or "",
            avatar=agent.avatar or "",
            owner_id=agent.owner_id or "",
            user_ids=agent.user_ids if agent.user_ids else [],
            owner_type=agent.owner_type or "",
            # Make sure these fields are explicitly set with default values if None
            is_bench_employee=bool(agent.is_bench_employee),  # Convert to bool explicitly
            is_imported=bool(agent.is_imported),
            # Rest of the fields...
            agent_category=agent.agent_category or "",
            system_message=agent.system_message or "",
            model_provider=model_provider,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            workflow_ids=agent.workflow_ids if agent.workflow_ids else [],
            mcp_server_ids=agent.mcp_server_ids if agent.mcp_server_ids else [],
            agent_topic_type=agent.agent_topic_type or "",
            visibility=agent.visibility or "",
            tags=agent.tags if agent.tags else [],
            status=agent.status or "",
            created_at=agent.created_at.isoformat(),
            updated_at=agent.updated_at.isoformat(),
            department=agent.department or "",
            organization_id=agent.organization_id or "",
            tone=agent.tone or "",
            files=[
                agent_pb2.FileEntry(
                    file=f.get("file", ""),
                    created_at=f.get("created_at", ""),
                    size=f.get("size", 0),
                )
                for f in files
            ],
            urls=[
                agent_pb2.UrlEntry(url=u.get("url", ""), created_at=u.get("created_at", ""))
                for u in urls
            ],
            is_changes_marketplace=(
                bool(agent.is_changes_marketplace)
                if agent.is_changes_marketplace is not None
                else False
            ),
            # New fields
            is_a2a=bool(agent.is_a2a) if agent.is_a2a is not None else False,
            is_customizable=(
                bool(agent.is_customizable) if agent.is_customizable is not None else False
            ),
            capabilities_id=str(agent.capabilities_id) if agent.capabilities_id else "",
            example_prompts=agent.example_prompts if agent.example_prompts else [],
            category=agent.category or "",
            is_updated=agent.is_updated if agent.is_updated else False,
        )

        # Populate model_data
        model_data_proto = agent_pb2.ModelData()
        model_data_proto.model_provider = model_provider
        model_data_proto.model_name = model_name
        model_data_proto.temperature = temperature
        model_data_proto.max_tokens = max_tokens

        # Fetch model_id and context_window from Model table
        if model_provider and model_name:
            model_entry = (
                db.query(Model)
                .join(Provider)
                .filter(Provider.provider == model_provider, Model.model == model_name)
                .first()
            )
            if model_entry:
                model_data_proto.model_id = str(model_entry.id)
                model_data_proto.context_window = (
                    model_entry.context_window if model_entry.context_window is not None else 0
                )

        agent_proto.model_data.CopyFrom(model_data_proto)

        if agent.capabilities_id:
            # agent_proto.capabilities_id is already set during initialization
            db_capabilities = (
                db.query(AgentCapabilities)
                .filter(AgentCapabilities.id == agent.capabilities_id)
                .first()
            )
            if db_capabilities:
                capabilities_proto = agent_pb2.AgentCapabilities(
                    id=str(db_capabilities.id),
                    capabilities=(
                        json.dumps(db_capabilities.capabilities)
                        if db_capabilities.capabilities
                        else ""
                    ),
                    input_modes=db_capabilities.input_modes if db_capabilities.input_modes else [],
                    output_modes=(
                        db_capabilities.output_modes if db_capabilities.output_modes else []
                    ),
                    response_model=(
                        db_capabilities.response_model if db_capabilities.response_model else []
                    ),
                    created_at=(
                        db_capabilities.created_at.isoformat() if db_capabilities.created_at else ""
                    ),
                    updated_at=(
                        db_capabilities.updated_at.isoformat() if db_capabilities.updated_at else ""
                    ),
                )
                agent_proto.agent_capabilities.CopyFrom(capabilities_proto)

        if agent.variables:  # agent.variables is List[AgentVariables (ORM)]
            logger.debug(f"ORM agent has {len(agent.variables)} variables. Converting to proto.")
            for db_var_orm_item in agent.variables:
                # Convert Python VariableTypeEnum (e.g., db_var_orm_item.type.value which is "text")
                # to Protobuf VariableType enum (int)
                var_type_proto_int_val = db_var_orm_item.type

                var_data_proto_item = agent_pb2.AgentVariableData(
                    id=str(db_var_orm_item.id),  # Include ID in response
                    name=db_var_orm_item.name,
                    type=var_type_proto_int_val,
                    created_at=db_var_orm_item.created_at.isoformat(),
                    updated_at=db_var_orm_item.updated_at.isoformat(),
                )
                if db_var_orm_item.description is not None:
                    var_data_proto_item.description = db_var_orm_item.description
                if (
                    db_var_orm_item.default_value is not None
                ):  # default_value is already string from DB
                    var_data_proto_item.default_value = db_var_orm_item.default_value

                agent_proto.variables.append(var_data_proto_item)
        return agent_proto
    except Exception as e:
        logger.error(f"Error converting AgentConfig to protobuf: {e}")
        raise e


def _agent_version_to_protobuf(
    version: AgentConfigVersion, is_current: bool = False
) -> agent_pb2.AgentVersion:
    """Convert AgentConfigVersion model to protobuf AgentVersion message"""
    try:
        version_proto = agent_pb2.AgentVersion(
            id=str(version.id),
            agent_config_id=str(version.agent_config_id),
            version_number=version.version_number,
            name=version.name,
            description=version.description,
            avatar=version.avatar,
            agent_category=version.agent_category,
            system_message=version.system_message,
            workflow_ids=version.workflow_ids if version.workflow_ids else [],
            mcp_server_ids=version.mcp_server_ids if version.mcp_server_ids else [],
            agent_topic_type=version.agent_topic_type if version.agent_topic_type else "",
            department=version.department if version.department else "",
            organization_id=version.organization_id if version.organization_id else "",
            tone=version.tone if version.tone else "",
            is_bench_employee=bool(version.is_bench_employee),
            is_changes_marketplace=(
                bool(version.is_changes_marketplace)
                if version.is_changes_marketplace is not None
                else False
            ),
            is_a2a=bool(version.is_a2a),
            is_customizable=bool(version.is_customizable),
            capabilities_id=str(version.capabilities_id) if version.capabilities_id else "",
            example_prompts=version.example_prompts if version.example_prompts else [],
            category=version.category if version.category else "",
            tags=version.tags if version.tags else [],
            status=version.status,
            version_notes=version.version_notes if version.version_notes else "",
            is_current=is_current,
            created_at=version.created_at.isoformat(),
            updated_at=version.updated_at.isoformat(),
        )

        # Add model config if available
        if version.model_config_id and version.model_config:
            model_config_proto = agent_pb2.AgentModelConfig(
                id=str(version.model_config.id),
                model_provider=(
                    version.model_config.model_provider
                    if version.model_config.model_provider
                    else ""
                ),
                model_name=(
                    version.model_config.model_name if version.model_config.model_name else ""
                ),
                temperature=(
                    version.model_config.temperature if version.model_config.temperature else 0.0
                ),
                max_tokens=(
                    version.model_config.max_tokens if version.model_config.max_tokens else 0
                ),
                created_at=version.model_config.created_at.isoformat(),
                updated_at=version.model_config.updated_at.isoformat(),
            )
            version_proto.model_config.CopyFrom(model_config_proto)

        # Add knowledge base if available
        if version.knowledge_base_id and version.knowledge_base:
            knowledge_base_proto = agent_pb2.AgentKnowledgeBase(
                id=str(version.knowledge_base.id),
                files=version.knowledge_base.files if version.knowledge_base.files else [],
                urls=version.knowledge_base.urls if version.knowledge_base.urls else [],
                created_at=version.knowledge_base.created_at.isoformat(),
                updated_at=version.knowledge_base.updated_at.isoformat(),
            )
            version_proto.knowledge_base.CopyFrom(knowledge_base_proto)

        return version_proto
    except Exception as e:
        logger.error(f"Error converting AgentConfigVersion to protobuf: {e}")
        raise e


def _ensure_agent_template_from_agent_config(
    db, agent_config: AgentConfig, create_new_if_missing: bool = True
):
    """
    Legacy function for template management - now deprecated.
    This function is kept for backward compatibility but does nothing.
    """
    logger.warning(
        "_ensure_agent_template_from_agent_config is deprecated - AgentTemplate model removed"
    )
    return None


def _marketplace_listing_to_protobuf(
    listing: AgentMarketplaceListing,
) -> agent_pb2.MarketplaceListing:
    """Convert AgentMarketplaceListing to protobuf MarketplaceListing."""
    return agent_pb2.MarketplaceListing(
        id=str(listing.id),
        agent_version_id=str(listing.agent_config_version_id),
        title=listing.title or "",
        description=listing.description or "",
        tags=listing.tags or [],
        category=listing.category or "",
        is_featured=listing.is_featured or False,
        download_count=listing.download_count or 0,
        rating=listing.rating or 0.0,
        created_at=listing.created_at.isoformat() if listing.created_at else "",
        updated_at=listing.updated_at.isoformat() if listing.updated_at else "",
    )


def _agent_version_list_to_protobuf(
    versions: List[AgentConfigVersion], current_version_id: str = None
) -> List[agent_pb2.AgentVersion]:
    """Convert list of AgentConfigVersion to protobuf AgentVersion list."""
    return [
        _agent_version_to_protobuf(version, is_current=(str(version.id) == current_version_id))
        for version in versions
    ]
